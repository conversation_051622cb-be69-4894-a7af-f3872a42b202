import { useFocusEffect } from 'expo-router';
import { useCallback, useEffect, useState } from 'react';
import { ActivityIndicator, Alert, FlatList, StyleSheet, Text, TextInput, TouchableOpacity, View } from 'react-native';
import { getAllStocks, StockInfo } from './stocks';
import { useCalendarStore, useFollowingStore } from './store';

const StockRow = ({ 
  item,
  following,
  onToggleFollow,
  recentDividend
}: { 
  item: StockInfo,
  following: boolean,
  onToggleFollow: (code: string) => void,
  recentDividend?: string | null
}) => (
  <TouchableOpacity 
    style={styles.stockRow}
    onLongPress={() => {
      if (following) {
        Alert.alert(
          '取消关注',
          `确定要取消关注 ${item.name} 吗？`,
          [
            { text: '关闭', style: 'cancel' },
            { text: '确定', onPress: () => onToggleFollow(item.code), style: 'destructive' },
          ],
          { cancelable: true }
        );
      }
    }}
  >
    <View>
      <Text style={styles.stockName}>{item.name}</Text>
      <Text style={styles.stockCode}>{item.code}</Text>
      {following && recentDividend && <Text style={styles.dividendText}>最近分红: {recentDividend}</Text>}
    </View>
    <TouchableOpacity 
      style={[styles.followButton, following ? styles.unfollowButton : styles.followButtonActive]} 
      onPress={() => onToggleFollow(item.code)}
    >
      <Text style={[styles.followButtonText, following ? styles.unfollowButtonText : {}]}>
        {following ? '已关注' : '关注'}
      </Text>
    </TouchableOpacity>
  </TouchableOpacity>
);

export default function FollowingScreen() {
  const [allStocks, setAllStocks] = useState<StockInfo[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredStocks, setFilteredStocks] = useState<StockInfo[]>([]);
  const [loading, setLoading] = useState(true);
  const { following, toggleFollow } = useFollowingStore();
  const { calendarData, fetchCalendarData } = useCalendarStore();

  const loadData = useCallback(async () => {
    setLoading(true);
    const stocks = await getAllStocks();
    setAllStocks(stocks);

    const today = new Date();
    const future = new Date();
    future.setDate(today.getDate() + 30);
    await fetchCalendarData(today.toISOString().split('T')[0], future.toISOString().split('T')[0]);

    setLoading(false);
  }, [fetchCalendarData]);

  useFocusEffect(
    useCallback(() => {
      loadData();
    }, [loadData])
  );

  useEffect(() => {
    if (searchQuery) {
      const lowerCaseQuery = searchQuery.toLowerCase();
      const filtered = allStocks.filter(stock => 
        stock.name.toLowerCase().includes(lowerCaseQuery) || 
        stock.code.includes(searchQuery) ||
        stock.pinyinInitial.toLowerCase().includes(lowerCaseQuery)
      );
      setFilteredStocks(filtered.slice(0, 50));
    } else {
      setFilteredStocks([]);
    }
  }, [searchQuery, allStocks]);

  const findRecentDividend = (stockCode: string) => {
    for (const day of calendarData) {
      if (day.list?.some(s => s.code === stockCode)) {
        return day.dateStr;
      }
    }
    return null;
  };

  const followedStocks = allStocks.filter(s => following.includes(s.code));

  if (loading) {
    return (
      <View style={styles.centered}>
        <ActivityIndicator size="large" />
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.searchContainer}>
        <TextInput
          placeholder="搜索股票代码、名称或拼音首字母..."
          value={searchQuery}
          onChangeText={setSearchQuery}
          style={styles.searchInput}
        />
        {searchQuery.length > 0 && (
          <TouchableOpacity onPress={() => setSearchQuery('')} style={styles.clearButton}>
            <Text style={styles.clearButtonText}>X</Text>
          </TouchableOpacity>
        )}
      </View>
      <FlatList
        data={searchQuery ? filteredStocks : followedStocks}
        keyExtractor={(item) => item.code}
        renderItem={({ item }) => (
          <StockRow 
            item={item} 
            following={following.includes(item.code)} 
            onToggleFollow={toggleFollow} 
            recentDividend={!searchQuery ? findRecentDividend(item.code) : null}
          />
        )}
        ListEmptyComponent={() => (
          !searchQuery && <Text style={styles.emptyText}>您还没有关注任何股票</Text>
        )}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f0f0f0',
  },
  centered: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f0f0f0',
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    margin: 10,
    backgroundColor: '#fff',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#ddd',
    paddingRight: 10, // Add padding for the clear button
  },
  searchInput: {
    flex: 1,
    padding: 12,
    fontSize: 16,
  },
  clearButton: {
    padding: 5,
  },
  clearButtonText: {
    fontSize: 18,
    color: '#888',
  },
  stockRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#fff',
    padding: 15,
    marginHorizontal: 10,
    marginVertical: 4,
    borderRadius: 8,
  },
  stockName: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  stockCode: {
    fontSize: 12,
    color: '#888',
    marginTop: 2,
  },
  dividendText: {
    fontSize: 12,
    color: '#d9534f',
    marginTop: 4,
  },
  followButton: {
    paddingVertical: 6,
    paddingHorizontal: 15,
    borderRadius: 20,
  },
  followButtonActive: {
    backgroundColor: '#28a745',
  },
  unfollowButton: {
    backgroundColor: '#e7e7e7',
  },
  followButtonText: {
    color: '#fff',
    fontWeight: 'bold',
  },
  unfollowButtonText: {
    color: '#555',
  },
  emptyText: {
    textAlign: 'center',
    marginTop: 50,
    color: '#888',
  },
});
