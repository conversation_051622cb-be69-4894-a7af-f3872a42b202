# 这是一个全新的Expo项目, 并安装了部分可能需要的库，请查看 package.json

## UI
TAB1: 日历
- 一个列表，有以下内容构成
- - 日期
- - 股票卡片(0个或多个，0个时显示文字)
- - - 代码(灰色小字) 名称 每100股派多少元(需要计算)
- - 一个展开按钮，显示未关注的股票卡片
TAB2: 关注
- 一个搜索框在顶部
- - 用于搜索添加关注股票，点击后进入搜索界面
- - 一个列表，有以下内容构成
- - 代码(灰色小字) 名称 关注/取消关注按钮
- 一个列表，有以下内容构成
- - 代码(灰色小字) 名称 最近分红时间(如果有) 长按显示菜单，菜单中有取消关注按钮

## API示例
这里只是示例，请勿照搬，请使用ts/tsx
### 获取分红
```js
var axios = require('axios');

var config = {
   method: 'get',
   url: 'https://finance.pae.baidu.com/sapi/v1/financecalendar?start_date=2025-07-28&end_date=2025-08-03&market=ab&pn=0&rn=20&cate=notify_divide&finClientType=pc',
   headers: { 
      'DNT': '1', 
      'Sec-GPC': '1', 
      'Priority': 'u=0', 
      'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 
      'Accept': '*/*', 
      'Host': 'finance.pae.baidu.com', 
      'Connection': 'keep-alive'
   }
};

axios(config)
.then(function (response) {
   console.log(JSON.stringify(response.data));
})
.catch(function (error) {
   console.log(error);
});

```

```json
{
    "QueryID": "18275277947968777337",
    "Result": {
        "calendarInfo": [
            {
                "date": "2025-07-28",
                "dateStr": "2025年07月28日 周一",
                "hasMore": "",
                "list": [
                    {
                        "capitalization": "352241579746",
                        "code": "601211",
                        "countryIcon": "https://baidu-finance.cdn.bcebos.com/imgs/money_icon_v2/DEFAULT.png",
                        "date": "2025-07-28",
                        "dbName": "calendar_notify_divide",
                        "diviCash": "2.80元",
                        "diviDate": "2025-07-28",
                        "exchange": "SH",
                        "isSkip": "1",
                        "logoInfo": {
                            "logo": "https://baidu-finance.cdn.bcebos.com/imgs/logo/stocks/stock_ab_601211.svg",
                            "type": "normal"
                        },
                        "market": "ab",
                        "marketValue": "352241579746",
                        "mountSection": {
                            "code": "490100",
                            "exchange": "BK",
                            "isSkip": "1",
                            "market": "ab",
                            "name": "证券Ⅱ",
                            "type": "block"
                        },
                        "name": "国泰海通",
                        "time": "--",
                        "type": "stock"
                    },
                    {
                        "capitalization": "79343509864",
                        "code": "601838",
                        "countryIcon": "https://baidu-finance.cdn.bcebos.com/imgs/money_icon_v2/DEFAULT.png",
                        "date": "2025-07-28",
                        "dbName": "calendar_notify_divide",
                        "diviCash": "8.91元",
                        "diviDate": "2025-07-28",
                        "exchange": "SH",
                        "isSkip": "1",
                        "logoInfo": {
                            "logo": "https://baidu-finance.cdn.bcebos.com/imgs/logo/stocks/stock_ab_601838.svg",
                            "type": "normal"
                        },
                        "market": "ab",
                        "marketValue": "79343509864",
                        "mountSection": {
                            "code": "480400",
                            "exchange": "BK",
                            "isSkip": "1",
                            "market": "ab",
                            "name": "城商行Ⅱ",
                            "type": "block"
                        },
                        "name": "成都银行",
                        "time": "--",
                        "type": "stock"
                    },
                    {
                        "capitalization": "26368626756",
                        "code": "600863",
                        "countryIcon": "https://baidu-finance.cdn.bcebos.com/imgs/money_icon_v2/DEFAULT.png",
                        "date": "2025-07-28",
                        "dbName": "calendar_notify_divide",
                        "diviCash": "2.20元",
                        "diviDate": "2025-07-28",
                        "exchange": "SH",
                        "isSkip": "1",
                        "logoInfo": {
                            "logo": "https://baidu-finance.cdn.bcebos.com/imgs/logo/stocks/ab_600863_1588.svg",
                            "type": "normal"
                        },
                        "market": "ab",
                        "marketValue": "26368626756",
                        "mountSection": {
                            "code": "410100",
                            "exchange": "BK",
                            "isSkip": "1",
                            "market": "ab",
                            "name": "电力",
                            "type": "block"
                        },
                        "name": "内蒙华电",
                        "time": "--",
                        "type": "stock"
                    },
                    {
                        "capitalization": "21598553428",
                        "code": "000967",
                        "countryIcon": "https://baidu-finance.cdn.bcebos.com/imgs/money_icon_v2/DEFAULT.png",
                        "date": "2025-07-28",
                        "dbName": "calendar_notify_divide",
                        "diviCash": "1.89元",
                        "diviDate": "2025-07-28",
                        "exchange": "SZ",
                        "isSkip": "1",
                        "logoInfo": {
                            "logo": "https://baidu-finance.cdn.bcebos.com/imgs/logo/stocks/stock_ab_000967.svg",
                            "type": "normal"
                        },
                        "market": "ab",
                        "marketValue": "21598553428",
                        "mountSection": {
                            "code": "760200",
                            "exchange": "BK",
                            "isSkip": "1",
                            "market": "ab",
                            "name": "环保设备Ⅱ",
                            "type": "block"
                        },
                        "name": "盈峰环境",
                        "time": "--",
                        "type": "stock"
                    },
                    {
                        "capitalization": "21203332413",
                        "code": "601608",
                        "countryIcon": "https://baidu-finance.cdn.bcebos.com/imgs/money_icon_v2/DEFAULT.png",
                        "date": "2025-07-28",
                        "dbName": "calendar_notify_divide",
                        "diviCash": "0.29元",
                        "diviDate": "2025-07-28",
                        "exchange": "SH",
                        "isSkip": "1",
                        "logoInfo": {
                            "logo": "https://baidu-finance.cdn.bcebos.com/imgs/logo/stocks/stock_ab_601608.svg",
                            "type": "normal"
                        },
                        "market": "ab",
                        "marketValue": "21203332413",
                        "mountSection": {
                            "code": "640200",
                            "exchange": "BK",
                            "isSkip": "1",
                            "market": "ab",
                            "name": "专用设备",
                            "type": "block"
                        },
                        "name": "中信重工",
                        "time": "--",
                        "type": "stock"
                    },
                    {
                        "capitalization": "17229722295",
                        "code": "002096",
                        "countryIcon": "https://baidu-finance.cdn.bcebos.com/imgs/money_icon_v2/DEFAULT.png",
                        "date": "2025-07-28",
                        "dbName": "calendar_notify_divide",
                        "diviCash": "2.30元",
                        "diviDate": "2025-07-28",
                        "exchange": "SZ",
                        "isSkip": "1",
                        "logoInfo": {
                            "logo": "https://baidu-finance.cdn.bcebos.com/imgs/logo/stocks/stock_ab_002096.svg",
                            "type": "normal"
                        },
                        "market": "ab",
                        "marketValue": "17229722295",
                        "mountSection": {
                            "code": "220300",
                            "exchange": "BK",
                            "isSkip": "1",
                            "market": "ab",
                            "name": "化学制品",
                            "type": "block"
                        },
                        "name": "易普力",
                        "time": "--",
                        "type": "stock"
                    },
                    {
                        "capitalization": "12700369353",
                        "code": "601900",
                        "countryIcon": "https://baidu-finance.cdn.bcebos.com/imgs/money_icon_v2/DEFAULT.png",
                        "date": "2025-07-28",
                        "dbName": "calendar_notify_divide",
                        "diviCash": "5.50元",
                        "diviDate": "2025-07-28",
                        "exchange": "SH",
                        "isSkip": "1",
                        "logoInfo": {
                            "logo": "https://baidu-finance.cdn.bcebos.com/imgs/logo/stocks/stock_ab_601900.svg",
                            "type": "normal"
                        },
                        "market": "ab",
                        "marketValue": "12700369353",
                        "mountSection": {
                            "code": "720900",
                            "exchange": "BK",
                            "isSkip": "1",
                            "market": "ab",
                            "name": "出版",
                            "type": "block"
                        },
                        "name": "南方传媒",
                        "time": "--",
                        "type": "stock"
                    },
                    {
                        "capitalization": "4212135514",
                        "code": "605399",
                        "countryIcon": "https://baidu-finance.cdn.bcebos.com/imgs/money_icon_v2/DEFAULT.png",
                        "date": "2025-07-28",
                        "dbName": "calendar_notify_divide",
                        "diviCash": "1.00元",
                        "diviDate": "2025-07-28",
                        "exchange": "SH",
                        "isSkip": "1",
                        "logoInfo": {
                            "logo": "https://baidu-finance.cdn.bcebos.com/imgs/logo/stocks/stock_ab_605399.svg",
                            "type": "normal"
                        },
                        "market": "ab",
                        "marketValue": "4212135514",
                        "mountSection": {
                            "code": "220300",
                            "exchange": "BK",
                            "isSkip": "1",
                            "market": "ab",
                            "name": "化学制品",
                            "type": "block"
                        },
                        "name": "晨光新材",
                        "time": "--",
                        "type": "stock"
                    },
                    {
                        "capitalization": "3671207971",
                        "code": "000897",
                        "countryIcon": "https://baidu-finance.cdn.bcebos.com/imgs/money_icon_v2/DEFAULT.png",
                        "date": "2025-07-28",
                        "dbName": "calendar_notify_divide",
                        "diviCash": "0.95元",
                        "diviDate": "2025-07-28",
                        "exchange": "SZ",
                        "isSkip": "1",
                        "logoInfo": {
                            "logo": "https://baidu-finance.cdn.bcebos.com/imgs/logo/stocks/stock_ab_000897.svg",
                            "type": "normal"
                        },
                        "market": "ab",
                        "marketValue": "3671207971",
                        "mountSection": {
                            "code": "430100",
                            "exchange": "BK",
                            "isSkip": "1",
                            "market": "ab",
                            "name": "房地产开发",
                            "type": "block"
                        },
                        "name": "津滨发展",
                        "time": "--",
                        "type": "stock"
                    }
                ],
                "total": 9
            },
            {
                "date": "2025-07-29",
                "dateStr": "2025年07月29日 周二",
                "hasMore": "",
                "list": [
                    {
                        "capitalization": "964599502377",
                        "code": "002594",
                        "countryIcon": "https://baidu-finance.cdn.bcebos.com/imgs/money_icon_v2/DEFAULT.png",
                        "date": "2025-07-29",
                        "dbName": "calendar_notify_divide",
                        "diviCash": "39.74元",
                        "diviDate": "2025-07-29",
                        "exchange": "SZ",
                        "isSkip": "1",
                        "logoInfo": {
                            "logo": "https://baidu-finance.cdn.bcebos.com/imgs/logo/stocks/stock_ab_002594.svg",
                            "type": "normal"
                        },
                        "market": "ab",
                        "marketValue": "964599502377",
                        "mountSection": {
                            "code": "280500",
                            "exchange": "BK",
                            "isSkip": "1",
                            "market": "ab",
                            "name": "乘用车",
                            "type": "block"
                        },
                        "name": "比亚迪",
                        "shareDivide": "10股送8.00股",
                        "time": "--",
                        "transfer": "10股转增12.00股",
                        "type": "stock"
                    },
                    {
                        "capitalization": "35034444008",
                        "code": "000729",
                        "countryIcon": "https://baidu-finance.cdn.bcebos.com/imgs/money_icon_v2/DEFAULT.png",
                        "date": "2025-07-29",
                        "dbName": "calendar_notify_divide",
                        "diviCash": "1.90元",
                        "diviDate": "2025-07-29",
                        "exchange": "SZ",
                        "isSkip": "1",
                        "logoInfo": {
                            "logo": "https://baidu-finance.cdn.bcebos.com/imgs/logo/stocks/stock_ab_000729.svg",
                            "type": "normal"
                        },
                        "market": "ab",
                        "marketValue": "35034444008",
                        "mountSection": {
                            "code": "340600",
                            "exchange": "BK",
                            "isSkip": "1",
                            "market": "ab",
                            "name": "非白酒",
                            "type": "block"
                        },
                        "name": "燕京啤酒",
                        "time": "--",
                        "type": "stock"
                    },
                    {
                        "capitalization": "26407500000",
                        "code": "002926",
                        "countryIcon": "https://baidu-finance.cdn.bcebos.com/imgs/money_icon_v2/DEFAULT.png",
                        "date": "2025-07-29",
                        "dbName": "calendar_notify_divide",
                        "diviCash": "0.85元",
                        "diviDate": "2025-07-29",
                        "exchange": "SZ",
                        "isSkip": "1",
                        "logoInfo": {
                            "logo": "https://baidu-finance.cdn.bcebos.com/imgs/logo/stocks/stock_ab_002926.svg",
                            "type": "normal"
                        },
                        "market": "ab",
                        "marketValue": "26407500000",
                        "mountSection": {
                            "code": "490100",
                            "exchange": "BK",
                            "isSkip": "1",
                            "market": "ab",
                            "name": "证券Ⅱ",
                            "type": "block"
                        },
                        "name": "华西证券",
                        "time": "--",
                        "type": "stock"
                    },
                    {
                        "capitalization": "21247119460",
                        "code": "600131",
                        "countryIcon": "https://baidu-finance.cdn.bcebos.com/imgs/money_icon_v2/DEFAULT.png",
                        "date": "2025-07-29",
                        "dbName": "calendar_notify_divide",
                        "diviCash": "1.71元",
                        "diviDate": "2025-07-29",
                        "exchange": "SH",
                        "isSkip": "1",
                        "logoInfo": {
                            "logo": "https://baidu-finance.cdn.bcebos.com/imgs/logo/stocks/stock_ab_600131.svg",
                            "type": "normal"
                        },
                        "market": "ab",
                        "marketValue": "21247119460",
                        "mountSection": {
                            "code": "710300",
                            "exchange": "BK",
                            "isSkip": "1",
                            "market": "ab",
                            "name": "IT服务Ⅱ",
                            "type": "block"
                        },
                        "name": "国网信通",
                        "time": "--",
                        "type": "stock"
                    },
                    {
                        "capitalization": "8823584418",
                        "code": "600851",
                        "countryIcon": "https://baidu-finance.cdn.bcebos.com/imgs/money_icon_v2/DEFAULT.png",
                        "date": "2025-07-29",
                        "dbName": "calendar_notify_divide",
                        "diviCash": "0.40元",
                        "diviDate": "2025-07-29",
                        "exchange": "SH",
                        "isSkip": "1",
                        "logoInfo": {
                            "logo": "https://baidu-finance.cdn.bcebos.com/imgs/logo/stocks/stock_ab_600851.svg",
                            "type": "normal"
                        },
                        "market": "ab",
                        "marketValue": "8823584418",
                        "mountSection": {
                            "code": "370100",
                            "exchange": "BK",
                            "isSkip": "1",
                            "market": "ab",
                            "name": "化学制药",
                            "type": "block"
                        },
                        "name": "海欣股份",
                        "time": "--",
                        "type": "stock"
                    },
                    {
                        "capitalization": "6360494425",
                        "code": "688535",
                        "countryIcon": "https://baidu-finance.cdn.bcebos.com/imgs/money_icon_v2/DEFAULT.png",
                        "date": "2025-07-29",
                        "dbName": "calendar_notify_divide",
                        "diviCash": "1.99元",
                        "diviDate": "2025-07-29",
                        "exchange": "SH",
                        "isSkip": "1",
                        "logoInfo": {
                            "logo": "https://baidu-finance.cdn.bcebos.com/imgs/logo/stocks/stock_ab_688535.svg",
                            "type": "normal"
                        },
                        "market": "ab",
                        "marketValue": "6360494425",
                        "mountSection": {
                            "code": "270100",
                            "exchange": "BK",
                            "isSkip": "1",
                            "market": "ab",
                            "name": "半导体",
                            "type": "block"
                        },
                        "name": "华海诚科",
                        "time": "--",
                        "type": "stock"
                    },
                    {
                        "capitalization": "3568071801",
                        "code": "600818",
                        "countryIcon": "https://baidu-finance.cdn.bcebos.com/imgs/money_icon_v2/DEFAULT.png",
                        "date": "2025-07-29",
                        "dbName": "calendar_notify_divide",
                        "diviCash": "0.10元",
                        "diviDate": "2025-07-29",
                        "exchange": "SH",
                        "isSkip": "1",
                        "logoInfo": {
                            "logo": "https://baidu-finance.cdn.bcebos.com/imgs/logo/stocks/stock_ab_600818.svg",
                            "type": "normal"
                        },
                        "market": "ab",
                        "marketValue": "3568071801",
                        "mountSection": {
                            "code": "280400",
                            "exchange": "BK",
                            "isSkip": "1",
                            "market": "ab",
                            "name": "摩托车及其他",
                            "type": "block"
                        },
                        "name": "中路股份",
                        "time": "--",
                        "type": "stock"
                    }
                ],
                "total": 7
            },
            {
                "date": "2025-07-30",
                "dateStr": "2025年07月30日 周三",
                "hasMore": "",
                "list": [
                    {
                        "capitalization": "71435259550",
                        "code": "600377",
                        "countryIcon": "https://baidu-finance.cdn.bcebos.com/imgs/money_icon_v2/DEFAULT.png",
                        "date": "2025-07-30",
                        "dbName": "calendar_notify_divide",
                        "diviCash": "4.90元",
                        "diviDate": "2025-07-30",
                        "exchange": "SH",
                        "isSkip": "1",
                        "logoInfo": {
                            "logo": "https://baidu-finance.cdn.bcebos.com/imgs/logo/stocks/stock_ab_600377.svg",
                            "type": "normal"
                        },
                        "market": "ab",
                        "marketValue": "71435259550",
                        "mountSection": {
                            "code": "420900",
                            "exchange": "BK",
                            "isSkip": "1",
                            "market": "ab",
                            "name": "铁路公路",
                            "type": "block"
                        },
                        "name": "宁沪高速",
                        "time": "--",
                        "type": "stock"
                    },
                    {
                        "capitalization": "27012837765",
                        "code": "600012",
                        "countryIcon": "https://baidu-finance.cdn.bcebos.com/imgs/money_icon_v2/DEFAULT.png",
                        "date": "2025-07-30",
                        "dbName": "calendar_notify_divide",
                        "diviCash": "6.04元",
                        "diviDate": "2025-07-30",
                        "exchange": "SH",
                        "isSkip": "1",
                        "logoInfo": {
                            "logo": "https://baidu-finance.cdn.bcebos.com/imgs/logo/stocks/stock_ab_600012.svg",
                            "type": "normal"
                        },
                        "market": "ab",
                        "marketValue": "27012837765",
                        "mountSection": {
                            "code": "420900",
                            "exchange": "BK",
                            "isSkip": "1",
                            "market": "ab",
                            "name": "铁路公路",
                            "type": "block"
                        },
                        "name": "皖通高速",
                        "time": "--",
                        "type": "stock"
                    },
                    {
                        "capitalization": "23156430696",
                        "code": "600848",
                        "countryIcon": "https://baidu-finance.cdn.bcebos.com/imgs/money_icon_v2/DEFAULT.png",
                        "date": "2025-07-30",
                        "dbName": "calendar_notify_divide",
                        "diviCash": "2.00元",
                        "diviDate": "2025-07-30",
                        "exchange": "SH",
                        "isSkip": "1",
                        "logoInfo": {
                            "logo": "https://baidu-finance.cdn.bcebos.com/imgs/logo/stocks/stock_ab_600848.svg",
                            "type": "normal"
                        },
                        "market": "ab",
                        "marketValue": "23156430696",
                        "mountSection": {
                            "code": "430100",
                            "exchange": "BK",
                            "isSkip": "1",
                            "market": "ab",
                            "name": "房地产开发",
                            "type": "block"
                        },
                        "name": "上海临港",
                        "time": "--",
                        "type": "stock"
                    },
                    {
                        "capitalization": "18096467123",
                        "code": "600739",
                        "countryIcon": "https://baidu-finance.cdn.bcebos.com/imgs/money_icon_v2/DEFAULT.png",
                        "date": "2025-07-30",
                        "dbName": "calendar_notify_divide",
                        "diviCash": "0.20元",
                        "diviDate": "2025-07-30",
                        "exchange": "SH",
                        "isSkip": "1",
                        "logoInfo": {
                            "logo": "https://baidu-finance.cdn.bcebos.com/imgs/logo/stocks/stock_ab_600739.svg",
                            "type": "normal"
                        },
                        "market": "ab",
                        "marketValue": "18096467123",
                        "mountSection": {
                            "code": "370300",
                            "exchange": "BK",
                            "isSkip": "1",
                            "market": "ab",
                            "name": "生物制品",
                            "type": "block"
                        },
                        "name": "辽宁成大",
                        "time": "--",
                        "type": "stock"
                    },
                    {
                        "capitalization": "12192970112",
                        "code": "603027",
                        "countryIcon": "https://baidu-finance.cdn.bcebos.com/imgs/money_icon_v2/DEFAULT.png",
                        "date": "2025-07-30",
                        "dbName": "calendar_notify_divide",
                        "diviCash": "5.00元",
                        "diviDate": "2025-07-30",
                        "exchange": "SH",
                        "isSkip": "1",
                        "logoInfo": {
                            "logo": "https://baidu-finance.cdn.bcebos.com/imgs/logo/stocks/stock_ab_603027.svg",
                            "type": "normal"
                        },
                        "market": "ab",
                        "marketValue": "12192970112",
                        "mountSection": {
                            "code": "340900",
                            "exchange": "BK",
                            "isSkip": "1",
                            "market": "ab",
                            "name": "调味发酵品Ⅱ",
                            "type": "block"
                        },
                        "name": "千禾味业",
                        "time": "--",
                        "type": "stock"
                    },
                    {
                        "capitalization": "11241089991",
                        "code": "600422",
                        "countryIcon": "https://baidu-finance.cdn.bcebos.com/imgs/money_icon_v2/DEFAULT.png",
                        "date": "2025-07-30",
                        "dbName": "calendar_notify_divide",
                        "diviCash": "3.00元",
                        "diviDate": "2025-07-30",
                        "exchange": "SH",
                        "isSkip": "1",
                        "logoInfo": {
                            "logo": "https://baidu-finance.cdn.bcebos.com/imgs/logo/stocks/stock_ab_600422.svg",
                            "type": "normal"
                        },
                        "market": "ab",
                        "marketValue": "11241089991",
                        "mountSection": {
                            "code": "370200",
                            "exchange": "BK",
                            "isSkip": "1",
                            "market": "ab",
                            "name": "中药Ⅱ",
                            "type": "block"
                        },
                        "name": "昆药集团",
                        "time": "--",
                        "type": "stock"
                    },
                    {
                        "capitalization": "9935320952",
                        "code": "601330",
                        "countryIcon": "https://baidu-finance.cdn.bcebos.com/imgs/money_icon_v2/DEFAULT.png",
                        "date": "2025-07-30",
                        "dbName": "calendar_notify_divide",
                        "diviCash": "2.00元",
                        "diviDate": "2025-07-30",
                        "exchange": "SH",
                        "isSkip": "1",
                        "logoInfo": {
                            "logo": "https://baidu-finance.cdn.bcebos.com/imgs/logo/stocks/stock_ab_601330.svg",
                            "type": "normal"
                        },
                        "market": "ab",
                        "marketValue": "9935320952",
                        "mountSection": {
                            "code": "760100",
                            "exchange": "BK",
                            "isSkip": "1",
                            "market": "ab",
                            "name": "环境治理",
                            "type": "block"
                        },
                        "name": "绿色动力",
                        "time": "--",
                        "type": "stock"
                    },
                    {
                        "capitalization": "8886145220",
                        "code": "603619",
                        "countryIcon": "https://baidu-finance.cdn.bcebos.com/imgs/money_icon_v2/DEFAULT.png",
                        "date": "2025-07-30",
                        "dbName": "calendar_notify_divide",
                        "diviCash": "3.00元",
                        "diviDate": "2025-07-30",
                        "exchange": "SH",
                        "isSkip": "1",
                        "logoInfo": {
                            "logo": "https://baidu-finance.cdn.bcebos.com/imgs/logo/stocks/stock_ab_603619.svg",
                            "type": "normal"
                        },
                        "market": "ab",
                        "marketValue": "8886145220",
                        "mountSection": {
                            "code": "750200",
                            "exchange": "BK",
                            "isSkip": "1",
                            "market": "ab",
                            "name": "油服工程",
                            "type": "block"
                        },
                        "name": "中曼石油",
                        "time": "--",
                        "type": "stock"
                    },
                    {
                        "capitalization": "5733297811",
                        "code": "002344",
                        "countryIcon": "https://baidu-finance.cdn.bcebos.com/imgs/money_icon_v2/DEFAULT.png",
                        "date": "2025-07-30",
                        "dbName": "calendar_notify_divide",
                        "diviCash": "0.38元",
                        "diviDate": "2025-07-30",
                        "exchange": "SZ",
                        "isSkip": "1",
                        "logoInfo": {
                            "logo": "https://baidu-finance.cdn.bcebos.com/imgs/logo/stocks/stock_ab_002344.svg",
                            "type": "normal"
                        },
                        "market": "ab",
                        "marketValue": "5733297811",
                        "mountSection": {
                            "code": "450300",
                            "exchange": "BK",
                            "isSkip": "1",
                            "market": "ab",
                            "name": "一般零售",
                            "type": "block"
                        },
                        "name": "海宁皮城",
                        "time": "--",
                        "type": "stock"
                    },
                    {
                        "capitalization": "4136571178",
                        "code": "002228",
                        "countryIcon": "https://baidu-finance.cdn.bcebos.com/imgs/money_icon_v2/DEFAULT.png",
                        "date": "2025-07-30",
                        "dbName": "calendar_notify_divide",
                        "diviCash": "1.80元",
                        "diviDate": "2025-07-30",
                        "exchange": "SZ",
                        "isSkip": "1",
                        "logoInfo": {
                            "logo": "https://baidu-finance.cdn.bcebos.com/imgs/logo/stocks/stock_ab_002228.svg",
                            "type": "normal"
                        },
                        "market": "ab",
                        "marketValue": "4136571178",
                        "mountSection": {
                            "code": "360200",
                            "exchange": "BK",
                            "isSkip": "1",
                            "market": "ab",
                            "name": "包装印刷",
                            "type": "block"
                        },
                        "name": "合兴包装",
                        "time": "--",
                        "type": "stock"
                    },
                    {
                        "capitalization": "2808884400",
                        "code": "301041",
                        "countryIcon": "https://baidu-finance.cdn.bcebos.com/imgs/money_icon_v2/DEFAULT.png",
                        "date": "2025-07-30",
                        "dbName": "calendar_notify_divide",
                        "diviCash": "1.00元",
                        "diviDate": "2025-07-30",
                        "exchange": "SZ",
                        "isSkip": "1",
                        "logoInfo": {
                            "logo": "https://baidu-finance.cdn.bcebos.com/imgs/logo/stocks/stock_ab_301041.svg",
                            "type": "normal"
                        },
                        "market": "ab",
                        "marketValue": "2808884400",
                        "mountSection": {
                            "code": "270200",
                            "exchange": "BK",
                            "isSkip": "1",
                            "market": "ab",
                            "name": "元件",
                            "type": "block"
                        },
                        "name": "金百泽",
                        "time": "--",
                        "type": "stock"
                    }
                ],
                "total": 11
            },
            {
                "date": "2025-07-31",
                "dateStr": "2025年07月31日 周四",
                "hasMore": "",
                "list": [
                    {
                        "capitalization": "655362429678",
                        "code": "601138",
                        "countryIcon": "https://baidu-finance.cdn.bcebos.com/imgs/money_icon_v2/DEFAULT.png",
                        "date": "2025-07-31",
                        "dbName": "calendar_notify_divide",
                        "diviCash": "6.40元",
                        "diviDate": "2025-07-31",
                        "exchange": "SH",
                        "isSkip": "1",
                        "logoInfo": {
                            "logo": "https://baidu-finance.cdn.bcebos.com/imgs/logo/stocks/stock_ab_601138.svg",
                            "type": "normal"
                        },
                        "market": "ab",
                        "marketValue": "655362429678",
                        "mountSection": {
                            "code": "270500",
                            "exchange": "BK",
                            "isSkip": "1",
                            "market": "ab",
                            "name": "消费电子",
                            "type": "block"
                        },
                        "name": "工业富联",
                        "time": "--",
                        "type": "stock"
                    },
                    {
                        "capitalization": "199442409437",
                        "code": "600104",
                        "countryIcon": "https://baidu-finance.cdn.bcebos.com/imgs/money_icon_v2/DEFAULT.png",
                        "date": "2025-07-31",
                        "dbName": "calendar_notify_divide",
                        "diviCash": "0.88元",
                        "diviDate": "2025-07-31",
                        "exchange": "SH",
                        "isSkip": "1",
                        "logoInfo": {
                            "logo": "https://baidu-finance.cdn.bcebos.com/imgs/logo/stocks/stock_ab_600104.svg",
                            "type": "normal"
                        },
                        "market": "ab",
                        "marketValue": "199442409437",
                        "mountSection": {
                            "code": "280500",
                            "exchange": "BK",
                            "isSkip": "1",
                            "market": "ab",
                            "name": "乘用车",
                            "type": "block"
                        },
                        "name": "上汽集团",
                        "time": "--",
                        "type": "stock"
                    },
                    {
                        "capitalization": "121097830391",
                        "code": "600436",
                        "countryIcon": "https://baidu-finance.cdn.bcebos.com/imgs/money_icon_v2/DEFAULT.png",
                        "date": "2025-07-31",
                        "dbName": "calendar_notify_divide",
                        "diviCash": "18.20元",
                        "diviDate": "2025-07-31",
                        "exchange": "SH",
                        "isSkip": "1",
                        "logoInfo": {
                            "logo": "https://baidu-finance.cdn.bcebos.com/imgs/logo/stocks/stock_ab_600436.svg",
                            "type": "normal"
                        },
                        "market": "ab",
                        "marketValue": "121097830391",
                        "mountSection": {
                            "code": "370200",
                            "exchange": "BK",
                            "isSkip": "1",
                            "market": "ab",
                            "name": "中药Ⅱ",
                            "type": "block"
                        },
                        "name": "片仔癀",
                        "time": "--",
                        "type": "stock"
                    },
                    {
                        "capitalization": "75842151821",
                        "code": "001965",
                        "countryIcon": "https://baidu-finance.cdn.bcebos.com/imgs/money_icon_v2/DEFAULT.png",
                        "date": "2025-07-31",
                        "dbName": "calendar_notify_divide",
                        "diviCash": "4.17元",
                        "diviDate": "2025-07-31",
                        "exchange": "SZ",
                        "isSkip": "1",
                        "logoInfo": {
                            "logo": "https://baidu-finance.cdn.bcebos.com/imgs/logo/stocks/stock_ab_001965.svg",
                            "type": "normal"
                        },
                        "market": "ab",
                        "marketValue": "75842151821",
                        "mountSection": {
                            "code": "420900",
                            "exchange": "BK",
                            "isSkip": "1",
                            "market": "ab",
                            "name": "铁路公路",
                            "type": "block"
                        },
                        "name": "招商公路",
                        "time": "--",
                        "type": "stock"
                    },
                    {
                        "capitalization": "32598209875",
                        "code": "601866",
                        "countryIcon": "https://baidu-finance.cdn.bcebos.com/imgs/money_icon_v2/DEFAULT.png",
                        "date": "2025-07-31",
                        "dbName": "calendar_notify_divide",
                        "diviCash": "0.19元",
                        "diviDate": "2025-07-31",
                        "exchange": "SH",
                        "isSkip": "1",
                        "logoInfo": {
                            "logo": "https://baidu-finance.cdn.bcebos.com/imgs/logo/stocks/ab_601866_6740.svg",
                            "type": "normal"
                        },
                        "market": "ab",
                        "marketValue": "32598209875",
                        "mountSection": {
                            "code": "421100",
                            "exchange": "BK",
                            "isSkip": "1",
                            "market": "ab",
                            "name": "航运港口",
                            "type": "block"
                        },
                        "name": "中远海发",
                        "time": "--",
                        "type": "stock"
                    },
                    {
                        "capitalization": "22226736000",
                        "code": "601001",
                        "countryIcon": "https://baidu-finance.cdn.bcebos.com/imgs/money_icon_v2/DEFAULT.png",
                        "date": "2025-07-31",
                        "dbName": "calendar_notify_divide",
                        "diviCash": "7.55元",
                        "diviDate": "2025-07-31",
                        "exchange": "SH",
                        "isSkip": "1",
                        "logoInfo": {
                            "logo": "https://baidu-finance.cdn.bcebos.com/imgs/logo/stocks/stock_ab_601001.svg",
                            "type": "normal"
                        },
                        "market": "ab",
                        "marketValue": "22226736000",
                        "mountSection": {
                            "code": "740100",
                            "exchange": "BK",
                            "isSkip": "1",
                            "market": "ab",
                            "name": "煤炭开采",
                            "type": "block"
                        },
                        "name": "晋控煤业",
                        "time": "--",
                        "type": "stock"
                    },
                    {
                        "capitalization": "21592833577",
                        "code": "600170",
                        "countryIcon": "https://baidu-finance.cdn.bcebos.com/imgs/money_icon_v2/DEFAULT.png",
                        "date": "2025-07-31",
                        "dbName": "calendar_notify_divide",
                        "diviCash": "0.60元",
                        "diviDate": "2025-07-31",
                        "exchange": "SH",
                        "isSkip": "1",
                        "logoInfo": {
                            "logo": "https://baidu-finance.cdn.bcebos.com/imgs/logo/stocks/stock_ab_600170.svg",
                            "type": "normal"
                        },
                        "market": "ab",
                        "marketValue": "21592833577",
                        "mountSection": {
                            "code": "620100",
                            "exchange": "BK",
                            "isSkip": "1",
                            "market": "ab",
                            "name": "房屋建设Ⅱ",
                            "type": "block"
                        },
                        "name": "上海建工",
                        "time": "--",
                        "type": "stock"
                    },
                    {
                        "capitalization": "19166342898",
                        "code": "688498",
                        "countryIcon": "https://baidu-finance.cdn.bcebos.com/imgs/money_icon_v2/DEFAULT.png",
                        "date": "2025-07-31",
                        "dbName": "calendar_notify_divide",
                        "diviCash": "1.00元",
                        "diviDate": "2025-07-31",
                        "exchange": "SH",
                        "isSkip": "1",
                        "logoInfo": {
                            "logo": "https://baidu-finance.cdn.bcebos.com/imgs/logo/stocks/stock_ab_688498.svg",
                            "type": "normal"
                        },
                        "market": "ab",
                        "marketValue": "19166342898",
                        "mountSection": {
                            "code": "270100",
                            "exchange": "BK",
                            "isSkip": "1",
                            "market": "ab",
                            "name": "半导体",
                            "type": "block"
                        },
                        "name": "源杰科技",
                        "time": "--",
                        "type": "stock"
                    },
                    {
                        "capitalization": "15606004000",
                        "code": "000628",
                        "countryIcon": "https://baidu-finance.cdn.bcebos.com/imgs/money_icon_v2/DEFAULT.png",
                        "date": "2025-07-31",
                        "dbName": "calendar_notify_divide",
                        "diviCash": "0.55元",
                        "diviDate": "2025-07-31",
                        "exchange": "SZ",
                        "isSkip": "1",
                        "logoInfo": {
                            "logo": "https://baidu-finance.cdn.bcebos.com/imgs/logo/stocks/stock_ab_000628.svg",
                            "type": "normal"
                        },
                        "market": "ab",
                        "marketValue": "15606004000",
                        "mountSection": {
                            "code": "620100",
                            "exchange": "BK",
                            "isSkip": "1",
                            "market": "ab",
                            "name": "房屋建设Ⅱ",
                            "type": "block"
                        },
                        "name": "高新发展",
                        "time": "--",
                        "type": "stock"
                    },
                    {
                        "capitalization": "15183931235",
                        "code": "000799",
                        "countryIcon": "https://baidu-finance.cdn.bcebos.com/imgs/money_icon_v2/DEFAULT.png",
                        "date": "2025-07-31",
                        "dbName": "calendar_notify_divide",
                        "diviCash": "6.00元",
                        "diviDate": "2025-07-31",
                        "exchange": "SZ",
                        "isSkip": "1",
                        "logoInfo": {
                            "logo": "https://baidu-finance.cdn.bcebos.com/imgs/logo/stocks/stock_ab_000799.svg",
                            "type": "normal"
                        },
                        "market": "ab",
                        "marketValue": "15183931235",
                        "mountSection": {
                            "code": "340500",
                            "exchange": "BK",
                            "isSkip": "1",
                            "market": "ab",
                            "name": "白酒Ⅱ",
                            "type": "block"
                        },
                        "name": "酒鬼酒",
                        "time": "--",
                        "type": "stock"
                    },
                    {
                        "capitalization": "11772165936",
                        "code": "600597",
                        "countryIcon": "https://baidu-finance.cdn.bcebos.com/imgs/money_icon_v2/DEFAULT.png",
                        "date": "2025-07-31",
                        "dbName": "calendar_notify_divide",
                        "diviCash": "1.60元",
                        "diviDate": "2025-07-31",
                        "exchange": "SH",
                        "isSkip": "1",
                        "logoInfo": {
                            "logo": "https://baidu-finance.cdn.bcebos.com/imgs/logo/stocks/stock_ab_600597.svg",
                            "type": "normal"
                        },
                        "market": "ab",
                        "marketValue": "11772165936",
                        "mountSection": {
                            "code": "340700",
                            "exchange": "BK",
                            "isSkip": "1",
                            "market": "ab",
                            "name": "饮料乳品",
                            "type": "block"
                        },
                        "name": "光明乳业",
                        "time": "--",
                        "type": "stock"
                    },
                    {
                        "capitalization": "6721238160",
                        "code": "600088",
                        "countryIcon": "https://baidu-finance.cdn.bcebos.com/imgs/money_icon_v2/DEFAULT.png",
                        "date": "2025-07-31",
                        "dbName": "calendar_notify_divide",
                        "diviCash": "0.34元",
                        "diviDate": "2025-07-31",
                        "exchange": "SH",
                        "isSkip": "1",
                        "logoInfo": {
                            "logo": "https://baidu-finance.cdn.bcebos.com/imgs/logo/stocks/stock_ab_600088.svg",
                            "type": "normal"
                        },
                        "market": "ab",
                        "marketValue": "6721238160",
                        "mountSection": {
                            "code": "720600",
                            "exchange": "BK",
                            "isSkip": "1",
                            "market": "ab",
                            "name": "影视院线",
                            "type": "block"
                        },
                        "name": "中视传媒",
                        "time": "--",
                        "type": "stock"
                    },
                    {
                        "capitalization": "6321252954",
                        "code": "600203",
                        "countryIcon": "https://baidu-finance.cdn.bcebos.com/imgs/money_icon_v2/DEFAULT.png",
                        "date": "2025-07-31",
                        "dbName": "calendar_notify_divide",
                        "diviCash": "0.20元",
                        "diviDate": "2025-07-31",
                        "exchange": "SH",
                        "isSkip": "1",
                        "logoInfo": {
                            "logo": "https://baidu-finance.cdn.bcebos.com/imgs/logo/stocks/stock_ab_600203.svg",
                            "type": "normal"
                        },
                        "market": "ab",
                        "marketValue": "6321252954",
                        "mountSection": {
                            "code": "270500",
                            "exchange": "BK",
                            "isSkip": "1",
                            "market": "ab",
                            "name": "消费电子",
                            "type": "block"
                        },
                        "name": "福日电子",
                        "time": "--",
                        "type": "stock"
                    },
                    {
                        "capitalization": "2746656000",
                        "code": "603048",
                        "countryIcon": "https://baidu-finance.cdn.bcebos.com/imgs/money_icon_v2/DEFAULT.png",
                        "date": "2025-07-31",
                        "dbName": "calendar_notify_divide",
                        "diviCash": "2.80元",
                        "diviDate": "2025-07-31",
                        "exchange": "SH",
                        "isSkip": "1",
                        "logoInfo": {
                            "logo": "https://baidu-finance.cdn.bcebos.com/imgs/logo/stocks/stock_ab_603048.svg",
                            "type": "normal"
                        },
                        "market": "ab",
                        "marketValue": "2746656000",
                        "mountSection": {
                            "code": "280200",
                            "exchange": "BK",
                            "isSkip": "1",
                            "market": "ab",
                            "name": "汽车零部件",
                            "type": "block"
                        },
                        "name": "浙江黎明",
                        "time": "--",
                        "type": "stock"
                    }
                ],
                "total": 14
            },
            {
                "date": "2025-08-01",
                "dateStr": "2025年08月01日 周五",
                "hasMore": "",
                "list": [
                    {
                        "capitalization": "145239142026",
                        "code": "603501",
                        "countryIcon": "https://baidu-finance.cdn.bcebos.com/imgs/money_icon_v2/DEFAULT.png",
                        "date": "2025-08-01",
                        "dbName": "calendar_notify_divide",
                        "diviCash": "2.20元",
                        "diviDate": "2025-08-01",
                        "exchange": "SH",
                        "isSkip": "1",
                        "logoInfo": {
                            "logo": "https://baidu-finance.cdn.bcebos.com/imgs/logo/stocks/stock_ab_603501.svg",
                            "type": "normal"
                        },
                        "market": "ab",
                        "marketValue": "145239142026",
                        "mountSection": {
                            "code": "270100",
                            "exchange": "BK",
                            "isSkip": "1",
                            "market": "ab",
                            "name": "半导体",
                            "type": "block"
                        },
                        "name": "豪威集团",
                        "time": "--",
                        "type": "stock"
                    },
                    {
                        "capitalization": "67274495145",
                        "code": "603893",
                        "countryIcon": "https://baidu-finance.cdn.bcebos.com/imgs/money_icon_v2/DEFAULT.png",
                        "date": "2025-08-01",
                        "dbName": "calendar_notify_divide",
                        "diviCash": "6.50元",
                        "diviDate": "2025-08-01",
                        "exchange": "SH",
                        "isSkip": "1",
                        "logoInfo": {
                            "logo": "https://baidu-finance.cdn.bcebos.com/imgs/logo/stocks/stock_ab_603893.svg",
                            "type": "normal"
                        },
                        "market": "ab",
                        "marketValue": "67274495145",
                        "mountSection": {
                            "code": "270100",
                            "exchange": "BK",
                            "isSkip": "1",
                            "market": "ab",
                            "name": "半导体",
                            "type": "block"
                        },
                        "name": "瑞芯微",
                        "time": "--",
                        "type": "stock"
                    },
                    {
                        "capitalization": "54742171871",
                        "code": "688082",
                        "countryIcon": "https://baidu-finance.cdn.bcebos.com/imgs/money_icon_v2/DEFAULT.png",
                        "date": "2025-08-01",
                        "dbName": "calendar_notify_divide",
                        "diviCash": "6.53元",
                        "diviDate": "2025-08-01",
                        "exchange": "SH",
                        "isSkip": "1",
                        "logoInfo": {
                            "logo": "https://baidu-finance.cdn.bcebos.com/imgs/logo/stocks/stock_ab_688082.svg",
                            "type": "normal"
                        },
                        "market": "ab",
                        "marketValue": "54742171871",
                        "mountSection": {
                            "code": "270100",
                            "exchange": "BK",
                            "isSkip": "1",
                            "market": "ab",
                            "name": "半导体",
                            "type": "block"
                        },
                        "name": "盛美上海",
                        "time": "--",
                        "type": "stock"
                    },
                    {
                        "capitalization": "34456013459",
                        "code": "688065",
                        "countryIcon": "https://baidu-finance.cdn.bcebos.com/imgs/money_icon_v2/DEFAULT.png",
                        "date": "2025-08-01",
                        "dbName": "calendar_notify_divide",
                        "diviCash": "3.99元",
                        "diviDate": "2025-08-01",
                        "exchange": "SH",
                        "isSkip": "1",
                        "logoInfo": {
                            "logo": "https://baidu-finance.cdn.bcebos.com/imgs/logo/stocks/stock_ab_688065.svg",
                            "type": "normal"
                        },
                        "market": "ab",
                        "marketValue": "34456013459",
                        "mountSection": {
                            "code": "220300",
                            "exchange": "BK",
                            "isSkip": "1",
                            "market": "ab",
                            "name": "化学制品",
                            "type": "block"
                        },
                        "name": "凯赛生物",
                        "time": "--",
                        "type": "stock"
                    },
                    {
                        "capitalization": "23760742577",
                        "code": "600754",
                        "countryIcon": "https://baidu-finance.cdn.bcebos.com/imgs/money_icon_v2/DEFAULT.png",
                        "date": "2025-08-01",
                        "dbName": "calendar_notify_divide",
                        "diviCash": "3.80元",
                        "diviDate": "2025-08-01",
                        "exchange": "SH",
                        "isSkip": "1",
                        "logoInfo": {
                            "logo": "https://baidu-finance.cdn.bcebos.com/imgs/logo/stocks/stock_ab_600754.svg",
                            "type": "normal"
                        },
                        "market": "ab",
                        "marketValue": "23760742577",
                        "mountSection": {
                            "code": "460900",
                            "exchange": "BK",
                            "isSkip": "1",
                            "market": "ab",
                            "name": "酒店餐饮",
                            "type": "block"
                        },
                        "name": "锦江酒店",
                        "time": "--",
                        "type": "stock"
                    },
                    {
                        "capitalization": "18002583669",
                        "code": "600959",
                        "countryIcon": "https://baidu-finance.cdn.bcebos.com/imgs/money_icon_v2/DEFAULT.png",
                        "date": "2025-08-01",
                        "dbName": "calendar_notify_divide",
                        "diviCash": "0.22元",
                        "diviDate": "2025-08-01",
                        "exchange": "SH",
                        "isSkip": "1",
                        "logoInfo": {
                            "logo": "https://baidu-finance.cdn.bcebos.com/imgs/logo/stocks/stock_ab_600959.svg",
                            "type": "normal"
                        },
                        "market": "ab",
                        "marketValue": "18002583669",
                        "mountSection": {
                            "code": "721000",
                            "exchange": "BK",
                            "isSkip": "1",
                            "market": "ab",
                            "name": "电视广播Ⅱ",
                            "type": "block"
                        },
                        "name": "江苏有线",
                        "time": "--",
                        "type": "stock"
                    },
                    {
                        "capitalization": "12926086031",
                        "code": "600116",
                        "countryIcon": "https://baidu-finance.cdn.bcebos.com/imgs/money_icon_v2/DEFAULT.png",
                        "date": "2025-08-01",
                        "dbName": "calendar_notify_divide",
                        "diviCash": "0.40元",
                        "diviDate": "2025-08-01",
                        "exchange": "SH",
                        "isSkip": "1",
                        "logoInfo": {
                            "logo": "https://baidu-finance.cdn.bcebos.com/imgs/logo/stocks/stock_ab_600116.svg",
                            "type": "normal"
                        },
                        "market": "ab",
                        "marketValue": "12926086031",
                        "mountSection": {
                            "code": "410100",
                            "exchange": "BK",
                            "isSkip": "1",
                            "market": "ab",
                            "name": "电力",
                            "type": "block"
                        },
                        "name": "三峡水利",
                        "time": "--",
                        "type": "stock"
                    },
                    {
                        "capitalization": "12075457820",
                        "code": "600635",
                        "countryIcon": "https://baidu-finance.cdn.bcebos.com/imgs/money_icon_v2/DEFAULT.png",
                        "date": "2025-08-01",
                        "dbName": "calendar_notify_divide",
                        "diviCash": "0.36元",
                        "diviDate": "2025-08-01",
                        "exchange": "SH",
                        "isSkip": "1",
                        "logoInfo": {
                            "logo": "https://baidu-finance.cdn.bcebos.com/imgs/logo/stocks/stock_ab_600635.svg",
                            "type": "normal"
                        },
                        "market": "ab",
                        "marketValue": "12075457820",
                        "mountSection": {
                            "code": "410300",
                            "exchange": "BK",
                            "isSkip": "1",
                            "market": "ab",
                            "name": "燃气Ⅱ",
                            "type": "block"
                        },
                        "name": "大众公用",
                        "time": "--",
                        "type": "stock"
                    },
                    {
                        "capitalization": "8154666310",
                        "code": "688236",
                        "countryIcon": "https://baidu-finance.cdn.bcebos.com/imgs/money_icon_v2/DEFAULT.png",
                        "date": "2025-08-01",
                        "dbName": "calendar_notify_divide",
                        "diviCash": "0.49元",
                        "diviDate": "2025-08-01",
                        "exchange": "SH",
                        "isSkip": "1",
                        "logoInfo": {
                            "logo": "https://baidu-finance.cdn.bcebos.com/imgs/logo/stocks/stock_ab_688236.svg",
                            "type": "normal"
                        },
                        "market": "ab",
                        "marketValue": "8154666310",
                        "mountSection": {
                            "code": "370500",
                            "exchange": "BK",
                            "isSkip": "1",
                            "market": "ab",
                            "name": "医疗器械",
                            "type": "block"
                        },
                        "name": "春立医疗",
                        "time": "--",
                        "type": "stock"
                    },
                    {
                        "capitalization": "2111000000",
                        "code": "001231",
                        "countryIcon": "https://baidu-finance.cdn.bcebos.com/imgs/money_icon_v2/DEFAULT.png",
                        "date": "2025-08-01",
                        "dbName": "calendar_notify_divide",
                        "diviCash": "1.50元",
                        "diviDate": "2025-08-01",
                        "exchange": "SZ",
                        "isSkip": "1",
                        "logoInfo": {
                            "logo": "https://baidu-finance.cdn.bcebos.com/imgs/logo/stocks/stock_ab_001231.svg",
                            "type": "normal"
                        },
                        "market": "ab",
                        "marketValue": "2111000000",
                        "mountSection": {
                            "code": "220800",
                            "exchange": "BK",
                            "isSkip": "1",
                            "market": "ab",
                            "name": "农化制品",
                            "type": "block"
                        },
                        "name": "农心科技",
                        "time": "--",
                        "type": "stock"
                    }
                ],
                "total": 10
            },
            {
                "date": "2025-08-02",
                "dateStr": "2025年08月02日 周六",
                "hasMore": "",
                "list": null,
                "total": 0
            },
            {
                "date": "2025-08-03",
                "dateStr": "2025年08月03日 周日",
                "hasMore": "",
                "list": null,
                "total": 0
            }
        ]
    },
    "ResultCode": 0
}
```

### 全部的股票和代码在list.csv中

```csv
code,name
301066,万事利
301053,远信工业
301317,鑫磊股份
300663,科蓝软件
300962,中金辐照
002689,远大智能
600448,华纺股份
...
```

### 持久化

请将关注的股票持久化
上面那个API的结果和请求时间也持久化，最小间隔24小时请求一次


### 通知

每晚上8点通知推送当日分红的股票，推送多条，标题为"分红: 股票信息"，内容为 "每100股分xx"


### 你可以基于用户体验自由调整功能/UI