import * as BackgroundFetch from 'expo-background-fetch';
import * as Notifications from 'expo-notifications';
import * as TaskManager from 'expo-task-manager';
import { Platform } from 'react-native';
import { getFinanceCalendar } from './api';
import { useFollowingStore } from './store';

const BACKGROUND_NOTIFICATION_TASK = 'BACKGROUND-NOTIFICATION-TASK';

Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldShowAlert: true,
    shouldPlaySound: true,
    shouldSetBadge: false,
    shouldShowBanner: true,
    shouldShowList: true,
  }),
});

TaskManager.defineTask(BACKGROUND_NOTIFICATION_TASK, async () => {
  try {
    const { following } = useFollowingStore.getState();
    if (following.length === 0) {
      return BackgroundFetch.BackgroundFetchResult.NoData;
    }

    const todayStr = new Date().toISOString().split('T')[0];
    const calendar = await getFinanceCalendar(todayStr, todayStr);
    const todayDividends = calendar.find(c => c.date === todayStr);

    let hasNotifications = false;

    if (todayDividends && todayDividends.list) {
      for (const stock of todayDividends.list) {
        if (following.includes(stock.code)) {
          await Notifications.scheduleNotificationAsync({
            content: {
              title: `分红提醒: ${stock.name} (${stock.code})`,
              body: `今日有分红，每100股派 ${stock.diviCash}`,
              data: { stockCode: stock.code, stockName: stock.name },
            },
            trigger: null, // Immediate notification
          });
          hasNotifications = true;
        }
      }
    }

    return hasNotifications
      ? BackgroundFetch.BackgroundFetchResult.NewData
      : BackgroundFetch.BackgroundFetchResult.NoData;
  } catch (error) {
    console.error('Background notification task failed:', error);
    return BackgroundFetch.BackgroundFetchResult.Failed;
  }
});

export const registerBackgroundFetchAsync = async () => {
  try {
    // Request notification permissions
    const { status } = await Notifications.requestPermissionsAsync();
    if (status !== 'granted') {
      console.warn('Notification permissions not granted');
      return false;
    }

    // Set up notification channel for Android
    if (Platform.OS === 'android') {
      await Notifications.setNotificationChannelAsync('dividend-alerts', {
        name: '分红提醒',
        importance: Notifications.AndroidImportance.HIGH,
        vibrationPattern: [0, 250, 250, 250],
        lightColor: '#FF231F7C',
      });
    }

    // Clear old notifications
    await Notifications.cancelAllScheduledNotificationsAsync();

    // Register background fetch task
    const isRegistered = await TaskManager.isTaskRegisteredAsync(BACKGROUND_NOTIFICATION_TASK);
    if (isRegistered) {
      await TaskManager.unregisterTaskAsync(BACKGROUND_NOTIFICATION_TASK);
    }

    await BackgroundFetch.registerTaskAsync(BACKGROUND_NOTIFICATION_TASK, {
      minimumInterval: 60 * 60 * 1000, // 1 hour minimum interval
      stopOnTerminate: false,
      startOnBoot: true,
    });

    console.log('Background notification task setup completed');
    return true;
  } catch (error) {
    console.error('Failed to register background fetch:', error);
    return false;
  }
};

// Function to manually check for dividends and send notifications
export const checkDividendsAndNotify = async () => {
  try {
    const { following } = useFollowingStore.getState();
    if (following.length === 0) {
      return;
    }

    const todayStr = new Date().toISOString().split('T')[0];
    const calendar = await getFinanceCalendar(todayStr, todayStr);
    const todayDividends = calendar.find(c => c.date === todayStr);

    if (todayDividends && todayDividends.list) {
      const followedDividends = todayDividends.list.filter(stock =>
        following.includes(stock.code)
      );

      if (followedDividends.length > 0) {
        // Send a summary notification
        await Notifications.scheduleNotificationAsync({
          content: {
            title: '今日分红提醒',
            body: `您关注的 ${followedDividends.length} 只股票今日有分红`,
            data: {
              type: 'dividend_summary',
              stocks: followedDividends.map(s => ({ code: s.code, name: s.name }))
            },
          },
          trigger: null, // Immediate notification
        });
      }
    }
  } catch (error) {
    console.error('Failed to check dividends:', error);
  }
};
