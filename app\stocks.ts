import * as FileSystem from 'expo-file-system';
import { Asset } from 'expo-asset';
import { pinyin } from 'pinyin-pro';

export interface StockInfo {
    code: string;
    name: string;
    pinyinInitial: string;
}

let allStocks: StockInfo[] = [];

export const getAllStocks = async (): Promise<StockInfo[]> => {
    if (allStocks.length > 0) {
        return allStocks;
    }

    const asset = Asset.fromModule(require('../assets/list.csv'));
    await asset.downloadAsync();

    if (!asset.localUri) {
        return [];
    }

    const csvData = await FileSystem.readAsStringAsync(asset.localUri);
    const lines = csvData.split('\n');
    const stocks: StockInfo[] = [];

    for (let i = 1; i < lines.length; i++) {
        const [code, name] = lines[i].split(',');
        if (code && name) {
            const trimmedName = name.trim();
            const pinyinInitial = pinyin(trimmedName, { pattern: 'first', toneType: 'none' }).replace(/ /g, '');
            stocks.push({ code, name: trimmedName, pinyinInitial });
        }
    }

    allStocks = stocks;
    return stocks;
};
