import axios from 'axios';
import AsyncStorage from '@react-native-async-storage/async-storage';

const API_CACHE_KEY = 'finance_calendar_cache';
const CACHE_DURATION = 24 * 60 * 60 * 1000; // 24 hours

export interface Stock { 
    code: string;
    name: string;
    diviCash: string;
}

export interface CalendarInfo {
    date: string;
    dateStr: string;
    list: Stock[] | null;
}

interface ApiResponse {
    Result: {
        calendarInfo: CalendarInfo[];
    };
}

export const getFinanceCalendar = async (startDate: string, endDate: string): Promise<CalendarInfo[]> => {
    const cachedData = await AsyncStorage.getItem(API_CACHE_KEY);
    if (cachedData) {
        const { timestamp, data } = JSON.parse(cachedData);
        if (Date.now() - timestamp < CACHE_DURATION) {
            return data;
        }
    }

    const response = await axios.get<ApiResponse>('https://finance.pae.baidu.com/sapi/v1/financecalendar', {
        params: {
            start_date: startDate,
            end_date: endDate,
            market: 'ab',
            pn: 0,
            rn: 100, // Fetch more results
            cate: 'notify_divide',
            finClientType: 'pc'
        },
        headers: {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.0.0 Safari/537.36',
        }
    });

    const calendarInfo = response.data.Result.calendarInfo;
    await AsyncStorage.setItem(API_CACHE_KEY, JSON.stringify({ timestamp: Date.now(), data: calendarInfo }));

    return calendarInfo;
};
